{"name": "gameflex-posts-lambda", "version": "1.0.0", "description": "GameFlex Posts Lambda Functions", "main": "dist/handler.js", "scripts": {"build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "package": "npm run build && npm prune --production"}, "dependencies": {"@aws-sdk/client-s3": "^3.450.0", "@aws-sdk/client-cognito-identity-provider": "^3.450.0", "@aws-sdk/client-dynamodb": "^3.450.0", "@aws-sdk/lib-dynamodb": "^3.450.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/aws-lambda": "^8.10.131", "@types/node": "^20.10.0", "@types/uuid": "^9.0.7", "typescript": "^5.3.2", "rimraf": "^5.0.5"}, "engines": {"node": ">=18.0.0"}}