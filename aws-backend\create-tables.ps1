# Quick DynamoDB table creation script
$env:AWS_ACCESS_KEY_ID = "test"
$env:AWS_SECRET_ACCESS_KEY = "test"
$env:AWS_DEFAULT_REGION = "us-east-1"
$ENDPOINT_URL = "http://localhost:45660"

Write-Host "[INFO] Creating Users table..." -ForegroundColor Green
aws --endpoint-url=$ENDPOINT_URL dynamodb create-table `
    --table-name Users `
    --attribute-definitions `
    AttributeName=id, AttributeType=S `
    AttributeName=cognito_user_id, AttributeType=S `
    AttributeName=email, AttributeType=S `
    --key-schema AttributeName=id, KeyType=HASH `
    --global-secondary-indexes `
    'IndexName=CognitoUserIdIndex,KeySchema=[{AttributeName=cognito_user_id,KeyType=HASH}],Projection={ProjectionType=ALL}' `
    'IndexName=EmailIndex,KeySchema=[{AttributeName=email,KeyType=HASH}],Projection={ProjectionType=ALL}' `
    --billing-mode PAY_PER_REQUEST

Write-Host "[INFO] Creating UserProfiles table..." -ForegroundColor Green
aws --endpoint-url=$ENDPOINT_URL dynamodb create-table `
    --table-name UserProfiles `
    --attribute-definitions AttributeName=user_id, AttributeType=S `
    --key-schema AttributeName=user_id, KeyType=HASH `
    --billing-mode PAY_PER_REQUEST

Write-Host "[INFO] Creating Channels table..." -ForegroundColor Green
aws --endpoint-url=$ENDPOINT_URL dynamodb create-table `
    --table-name Channels `
    --attribute-definitions `
    AttributeName=id, AttributeType=S `
    AttributeName=owner_id, AttributeType=S `
    --key-schema AttributeName=id, KeyType=HASH `
    --global-secondary-indexes `
    'IndexName=OwnerIdIndex,KeySchema=[{AttributeName=owner_id,KeyType=HASH}],Projection={ProjectionType=ALL},BillingMode=PAY_PER_REQUEST' `
    --billing-mode PAY_PER_REQUEST

Write-Host "[INFO] Creating ChannelMembers table..." -ForegroundColor Green
aws --endpoint-url=$ENDPOINT_URL dynamodb create-table `
    --table-name ChannelMembers `
    --attribute-definitions `
    AttributeName=channel_id, AttributeType=S `
    AttributeName=user_id, AttributeType=S `
    --key-schema `
    AttributeName=channel_id, KeyType=HASH `
    AttributeName=user_id, KeyType=RANGE `
    --global-secondary-indexes `
    'IndexName=UserIdIndex,KeySchema=[{AttributeName=user_id,KeyType=HASH},{AttributeName=channel_id,KeyType=RANGE}],Projection={ProjectionType=ALL},BillingMode=PAY_PER_REQUEST' `
    --billing-mode PAY_PER_REQUEST

Write-Host "[INFO] Creating Media table..." -ForegroundColor Green
aws --endpoint-url=$ENDPOINT_URL dynamodb create-table `
    --table-name Media `
    --attribute-definitions `
    AttributeName=id, AttributeType=S `
    AttributeName=owner_id, AttributeType=S `
    AttributeName=channel_id, AttributeType=S `
    --key-schema AttributeName=id, KeyType=HASH `
    --global-secondary-indexes `
    'IndexName=OwnerIdIndex,KeySchema=[{AttributeName=owner_id,KeyType=HASH}],Projection={ProjectionType=ALL},BillingMode=PAY_PER_REQUEST' `
    'IndexName=ChannelIdIndex,KeySchema=[{AttributeName=channel_id,KeyType=HASH}],Projection={ProjectionType=ALL},BillingMode=PAY_PER_REQUEST' `
    --billing-mode PAY_PER_REQUEST

Write-Host "[INFO] All tables created successfully!" -ForegroundColor Green
