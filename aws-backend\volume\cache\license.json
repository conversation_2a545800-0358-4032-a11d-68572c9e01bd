{"id": "fb507cdc-3379-43bb-a95a-683e746afd95", "license_format": "1", "signature": "9aef52dd129271b323463666daeee46976cd4368d8921b50c2c41352b1a9e748", "license_type": "trial", "issue_date": "2025-07-07T09:12:47+00:00", "expiry_date": "2025-07-21T08:46:18+00:00", "products": [{"name": "localstack.aws.provider/redshift:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/events:v2", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/application-autoscaling:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/mediastore-data:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/ssm:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/codestar-connections:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/textract:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/cloudfront:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/opensearch:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/dynamodbstreams:default", "version": "*", "version_operator": "=="}, {"name": "localstack.pods.remote/platform", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/kinesisanalytics:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.platform.plugin/certificates", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/ecr:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/secretsmanager:default", "version": "*", "version_operator": "=="}, {"name": "localstack.platform.plugin/ecs-k8s-task-executor", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/es:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/dynamodb:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/apigatewaymanagementapi:next_gen_pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/logs:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/swf:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/elb:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/bedrock:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/redshift:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/transfer:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.platform.plugin/iam-stream", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/apigateway:legacy_pro", "version": "*", "version_operator": "=="}, {"name": "localstack.container.runtime/docker", "version": "*", "version_operator": "=="}, {"name": "localstack.lambda.runtime_executor/docker", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/sns:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/identitystore:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/apigatewayv2:next_gen_pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/sagemaker-runtime:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/cloudwatch:v1", "version": "*", "version_operator": "=="}, {"name": "localstack.lambda.runtime_executor/kubernetes", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/events:v1_pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/events:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.pods.remote/s3", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/dynamodb:v2_pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/kinesisanalyticsv2:legacy", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/cognito-idp:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/ce:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/cloudwatch:v2", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/codeartifact:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/config:default", "version": "*", "version_operator": "=="}, {"name": "localstack.pods.public", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/events:legacy_pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/codecommit:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/timestream-write:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/eks:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/ses:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/fis:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/route53resolver:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/kafka:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/logs:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/apigatewayv2:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/iotanalytics:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/elasticbeanstalk:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/lakeformation:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/scheduler:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/codeconnections:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/ec2:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/sesv2:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/rds-data:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/neptune:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/efs:pro", "version": "*", "version_operator": "=="}, {"name": "localstack-pro", "version": "4.4.0", "version_operator": ">="}, {"name": "localstack.aws.provider/cognito-identity:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/codebuild:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/appconfigdata:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.container.runtime/kubernetes", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/mwaa:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/firehose:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/resource-groups:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/glue:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.platform.plugin/chaos", "version": "*", "version_operator": "=="}, {"name": "localstack.platform.plugin/lambda-esm-kafka", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/support:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/redshift-data:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/events:legacy", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/lambda:asf", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/eks:mock", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/lambda:v2", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/docdb:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/rds:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/cloudtrail:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/managedblockchain:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/dms:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/kms:default", "version": "*", "version_operator": "=="}, {"name": "localstack.glue.job_executor/docker", "version": "*", "version_operator": "=="}, {"name": "localstack.stacks.preview", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/apigateway:legacy", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/emr:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/lambda:v2_pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/organizations:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/verifiedpermissions:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/codepipeline:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/mediastore:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/ses:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/iot:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.pods.remote/oras", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/account:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.platform.plugin/chaos-basic", "version": "*", "version_operator": "=="}, {"name": "localstack.platform.plugin/iam-enforcement", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/ec2:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/autoscaling:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/snowflake:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/s3control:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/dynamodb:v2", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/cloudformation:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/kinesis:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/lambda:asf_pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/ssm:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/shield:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/pinpoint:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/route53:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/qldb:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.k8s.operator", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/sts:default", "version": "*", "version_operator": "=="}, {"name": "localstack.platform.plugin/ecs-firelens", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/resourcegroupstaggingapi:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/events:v2_pro", "version": "*", "version_operator": "=="}, {"name": "localstack.platform.plugin/non-default-partitions", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/s3:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/opensearch:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/appsync:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/ecs:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/xray:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/elastictranscoder:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/apigateway:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/mq:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/kinesisanalyticsv2:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/apigatewaymanagementapi:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/events:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/apigatewayv2:legacy_pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/dynamodbstreams:v2", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/elbv2:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/apigateway:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/elasticache:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/cloudwatch:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/route53:default", "version": "*", "version_operator": "=="}, {"name": "localstack.platform.plugin/replicator", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/wafv2:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/emr-serverless:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/events:v1", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/apigatewaymanagementapi:legacy_pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/memorydb:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/amplify:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/servicediscovery:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/acm:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/lambda:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/backup:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/apigateway:next_gen", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/codedeploy:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/appconfig:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/sts:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/apigateway:next_gen_pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/sqs:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/acm-pca:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/bedrock-runtime:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/dynamodb:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.glue.job_executor/kubernetes", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/timestream-query:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/sso-admin:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/athena:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/cloudcontrol:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/serverlessrepo:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/ram:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/stepfunctions:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/qldb-session:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/sqs:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/iot-data:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/pipes:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.platform.plugin/fis-emulation", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/mediaconvert:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/iotwireless:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/lambda:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/s3:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/cloudformation:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/iam:default", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/stepfunctions:v2", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/sagemaker:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/batch:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/transcribe:default", "version": "*", "version_operator": "=="}, {"name": "localstack.stacks.premium", "version": "*", "version_operator": "=="}, {"name": "localstack.aws.provider/glacier:pro", "version": "*", "version_operator": "=="}, {"name": "localstack.glue.job_executor/local", "version": "*", "version_operator": "=="}, {"name": "localstack.pods", "version": "*", "version_operator": "=="}], "license_status": "ACTIVE", "license_secret": "TFMxLjA6lM1mw8/V5RPifmAKNq6kPWg+mGAahdOqQxR9Qv01taOs+P2Yo0t9shuxjqGNTAdY", "last_activated": "2025-07-07T09:12:47.404485Z", "reactivate_after": "2025-07-08T09:12:47+00:00", "offline_data": {"localstack_version": "4.6.1.dev4"}}