# GameFlex DynamoDB Tables Creation Script (PowerShell)
# This script creates all DynamoDB tables for the GameFlex application

param(
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

# AWS CLI configuration for LocalStack
$env:AWS_ACCESS_KEY_ID = "test"
$env:AWS_SECRET_ACCESS_KEY = "test"
$env:AWS_DEFAULT_REGION = "us-east-1"
$ENDPOINT_URL = "http://localhost:45660"

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "[DYNAMODB] $Message" -ForegroundColor Blue
}

# Function to create a DynamoDB table
function New-DynamoDBTable {
    param(
        [string]$TableName,
        [hashtable]$TableDefinition
    )
    
    try {
        # Check if table exists
        $existingTable = aws --endpoint-url=$ENDPOINT_URL dynamodb describe-table --table-name $TableName 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Status "Table $TableName already exists"
            return $true
        }
        
        # Create table
        $tableJson = $TableDefinition | ConvertTo-Json -Depth 10
        $tableJson | Out-File -FilePath "temp-table-$TableName.json" -Encoding UTF8
        
        aws --endpoint-url=$ENDPOINT_URL dynamodb create-table --cli-input-json file://temp-table-$TableName.json | Out-Null
        Remove-Item "temp-table-$TableName.json" -Force
        
        if ($LASTEXITCODE -eq 0) {
            Write-Status "Created DynamoDB table: $TableName"
            
            # Wait for table to be active
            Write-Status "Waiting for table $TableName to be active..."
            do {
                Start-Sleep -Seconds 2
                $status = aws --endpoint-url=$ENDPOINT_URL dynamodb describe-table --table-name $TableName --query "Table.TableStatus" --output text 2>$null
            } while ($status -ne "ACTIVE" -and $LASTEXITCODE -eq 0)
            
            Write-Status "Table $TableName is now active"
            return $true
        }
        else {
            Write-Error "Failed to create table $TableName"
            return $false
        }
    }
    catch {
        Write-Error "Failed to create table $TableName : $_"
        return $false
    }
}

# Create Users table
function New-UsersTable {
    Write-Status "Creating Users table..."
    
    $tableDefinition = @{
        TableName              = "Users"
        AttributeDefinitions   = @(
            @{ AttributeName = "id"; AttributeType = "S" },
            @{ AttributeName = "cognito_user_id"; AttributeType = "S" },
            @{ AttributeName = "email"; AttributeType = "S" }
        )
        KeySchema              = @(
            @{ AttributeName = "id"; KeyType = "HASH" }
        )
        GlobalSecondaryIndexes = @(
            @{
                IndexName   = "CognitoUserIdIndex"
                KeySchema   = @(
                    @{ AttributeName = "cognito_user_id"; KeyType = "HASH" }
                )
                Projection  = @{ ProjectionType = "ALL" }
                BillingMode = "PAY_PER_REQUEST"
            },
            @{
                IndexName   = "EmailIndex"
                KeySchema   = @(
                    @{ AttributeName = "email"; KeyType = "HASH" }
                )
                Projection  = @{ ProjectionType = "ALL" }
                BillingMode = "PAY_PER_REQUEST"
            }
        )
        BillingMode            = "PAY_PER_REQUEST"
    }
    
    return New-DynamoDBTable -TableName "Users" -TableDefinition $tableDefinition
}

# Create UserProfiles table
function New-UserProfilesTable {
    Write-Status "Creating UserProfiles table..."
    
    $tableDefinition = @{
        TableName            = "UserProfiles"
        AttributeDefinitions = @(
            @{ AttributeName = "user_id"; AttributeType = "S" }
        )
        KeySchema            = @(
            @{ AttributeName = "user_id"; KeyType = "HASH" }
        )
        BillingMode          = "PAY_PER_REQUEST"
    }
    
    return New-DynamoDBTable -TableName "UserProfiles" -TableDefinition $tableDefinition
}

# Create Channels table
function New-ChannelsTable {
    Write-Status "Creating Channels table..."
    
    $tableDefinition = @{
        TableName              = "Channels"
        AttributeDefinitions   = @(
            @{ AttributeName = "id"; AttributeType = "S" },
            @{ AttributeName = "owner_id"; AttributeType = "S" }
        )
        KeySchema              = @(
            @{ AttributeName = "id"; KeyType = "HASH" }
        )
        GlobalSecondaryIndexes = @(
            @{
                IndexName   = "OwnerIdIndex"
                KeySchema   = @(
                    @{ AttributeName = "owner_id"; KeyType = "HASH" }
                )
                Projection  = @{ ProjectionType = "ALL" }
                BillingMode = "PAY_PER_REQUEST"
            }
        )
        BillingMode            = "PAY_PER_REQUEST"
    }
    
    return New-DynamoDBTable -TableName "Channels" -TableDefinition $tableDefinition
}

# Create ChannelMembers table
function New-ChannelMembersTable {
    Write-Status "Creating ChannelMembers table..."
    
    $tableDefinition = @{
        TableName              = "ChannelMembers"
        AttributeDefinitions   = @(
            @{ AttributeName = "channel_id"; AttributeType = "S" },
            @{ AttributeName = "user_id"; AttributeType = "S" }
        )
        KeySchema              = @(
            @{ AttributeName = "channel_id"; KeyType = "HASH" },
            @{ AttributeName = "user_id"; KeyType = "RANGE" }
        )
        GlobalSecondaryIndexes = @(
            @{
                IndexName   = "UserIdIndex"
                KeySchema   = @(
                    @{ AttributeName = "user_id"; KeyType = "HASH" },
                    @{ AttributeName = "channel_id"; KeyType = "RANGE" }
                )
                Projection  = @{ ProjectionType = "ALL" }
                BillingMode = "PAY_PER_REQUEST"
            }
        )
        BillingMode            = "PAY_PER_REQUEST"
    }
    
    return New-DynamoDBTable -TableName "ChannelMembers" -TableDefinition $tableDefinition
}

# Create Media table
function New-MediaTable {
    Write-Status "Creating Media table..."
    
    $tableDefinition = @{
        TableName              = "Media"
        AttributeDefinitions   = @(
            @{ AttributeName = "id"; AttributeType = "S" },
            @{ AttributeName = "owner_id"; AttributeType = "S" },
            @{ AttributeName = "channel_id"; AttributeType = "S" }
        )
        KeySchema              = @(
            @{ AttributeName = "id"; KeyType = "HASH" }
        )
        GlobalSecondaryIndexes = @(
            @{
                IndexName   = "OwnerIdIndex"
                KeySchema   = @(
                    @{ AttributeName = "owner_id"; KeyType = "HASH" }
                )
                Projection  = @{ ProjectionType = "ALL" }
                BillingMode = "PAY_PER_REQUEST"
            },
            @{
                IndexName   = "ChannelIdIndex"
                KeySchema   = @(
                    @{ AttributeName = "channel_id"; KeyType = "HASH" }
                )
                Projection  = @{ ProjectionType = "ALL" }
                BillingMode = "PAY_PER_REQUEST"
            }
        )
        BillingMode            = "PAY_PER_REQUEST"
    }
    
    return New-DynamoDBTable -TableName "Media" -TableDefinition $tableDefinition
}

# Create Posts table
function New-PostsTable {
    Write-Status "Creating Posts table..."

    $tableDefinition = @{
        TableName              = "Posts"
        AttributeDefinitions   = @(
            @{ AttributeName = "id"; AttributeType = "S" },
            @{ AttributeName = "author_id"; AttributeType = "S" },
            @{ AttributeName = "channel_id"; AttributeType = "S" },
            @{ AttributeName = "created_at"; AttributeType = "S" }
        )
        KeySchema              = @(
            @{ AttributeName = "id"; KeyType = "HASH" }
        )
        GlobalSecondaryIndexes = @(
            @{
                IndexName   = "AuthorIdCreatedAtIndex"
                KeySchema   = @(
                    @{ AttributeName = "author_id"; KeyType = "HASH" },
                    @{ AttributeName = "created_at"; KeyType = "RANGE" }
                )
                Projection  = @{ ProjectionType = "ALL" }
                BillingMode = "PAY_PER_REQUEST"
            },
            @{
                IndexName   = "ChannelIdCreatedAtIndex"
                KeySchema   = @(
                    @{ AttributeName = "channel_id"; KeyType = "HASH" },
                    @{ AttributeName = "created_at"; KeyType = "RANGE" }
                )
                Projection  = @{ ProjectionType = "ALL" }
                BillingMode = "PAY_PER_REQUEST"
            }
        )
        BillingMode            = "PAY_PER_REQUEST"
    }

    return New-DynamoDBTable -TableName "Posts" -TableDefinition $tableDefinition
}

# Create Comments table
function New-CommentsTable {
    Write-Status "Creating Comments table..."

    $tableDefinition = @{
        TableName              = "Comments"
        AttributeDefinitions   = @(
            @{ AttributeName = "post_id"; AttributeType = "S" },
            @{ AttributeName = "id"; AttributeType = "S" },
            @{ AttributeName = "author_id"; AttributeType = "S" },
            @{ AttributeName = "created_at"; AttributeType = "S" }
        )
        KeySchema              = @(
            @{ AttributeName = "post_id"; KeyType = "HASH" },
            @{ AttributeName = "id"; KeyType = "RANGE" }
        )
        GlobalSecondaryIndexes = @(
            @{
                IndexName   = "AuthorIdCreatedAtIndex"
                KeySchema   = @(
                    @{ AttributeName = "author_id"; KeyType = "HASH" },
                    @{ AttributeName = "created_at"; KeyType = "RANGE" }
                )
                Projection  = @{ ProjectionType = "ALL" }
                BillingMode = "PAY_PER_REQUEST"
            }
        )
        BillingMode            = "PAY_PER_REQUEST"
    }

    return New-DynamoDBTable -TableName "Comments" -TableDefinition $tableDefinition
}

# Create Likes table
function New-LikesTable {
    Write-Status "Creating Likes table..."

    $tableDefinition = @{
        TableName            = "Likes"
        AttributeDefinitions = @(
            @{ AttributeName = "post_id"; AttributeType = "S" },
            @{ AttributeName = "user_id"; AttributeType = "S" }
        )
        KeySchema            = @(
            @{ AttributeName = "post_id"; KeyType = "HASH" },
            @{ AttributeName = "user_id"; KeyType = "RANGE" }
        )
        BillingMode          = "PAY_PER_REQUEST"
    }

    return New-DynamoDBTable -TableName "Likes" -TableDefinition $tableDefinition
}

# Create Follows table
function New-FollowsTable {
    Write-Status "Creating Follows table..."

    $tableDefinition = @{
        TableName              = "Follows"
        AttributeDefinitions   = @(
            @{ AttributeName = "follower_id"; AttributeType = "S" },
            @{ AttributeName = "following_id"; AttributeType = "S" }
        )
        KeySchema              = @(
            @{ AttributeName = "follower_id"; KeyType = "HASH" },
            @{ AttributeName = "following_id"; KeyType = "RANGE" }
        )
        GlobalSecondaryIndexes = @(
            @{
                IndexName   = "FollowingIdIndex"
                KeySchema   = @(
                    @{ AttributeName = "following_id"; KeyType = "HASH" },
                    @{ AttributeName = "follower_id"; KeyType = "RANGE" }
                )
                Projection  = @{ ProjectionType = "ALL" }
                BillingMode = "PAY_PER_REQUEST"
            }
        )
        BillingMode            = "PAY_PER_REQUEST"
    }

    return New-DynamoDBTable -TableName "Follows" -TableDefinition $tableDefinition
}

# Create Notifications table
function New-NotificationsTable {
    Write-Status "Creating Notifications table..."

    $tableDefinition = @{
        TableName            = "Notifications"
        AttributeDefinitions = @(
            @{ AttributeName = "user_id"; AttributeType = "S" },
            @{ AttributeName = "created_at"; AttributeType = "S" }
        )
        KeySchema            = @(
            @{ AttributeName = "user_id"; KeyType = "HASH" },
            @{ AttributeName = "created_at"; KeyType = "RANGE" }
        )
        BillingMode          = "PAY_PER_REQUEST"
    }

    return New-DynamoDBTable -TableName "Notifications" -TableDefinition $tableDefinition
}

# Main execution
function Main {
    Write-Header "Creating DynamoDB tables for GameFlex..."
    Write-Host ""

    $success = $true

    if (-not (New-UsersTable)) { $success = $false }
    if (-not (New-UserProfilesTable)) { $success = $false }
    if (-not (New-ChannelsTable)) { $success = $false }
    if (-not (New-ChannelMembersTable)) { $success = $false }
    if (-not (New-MediaTable)) { $success = $false }
    if (-not (New-PostsTable)) { $success = $false }
    if (-not (New-CommentsTable)) { $success = $false }
    if (-not (New-LikesTable)) { $success = $false }
    if (-not (New-FollowsTable)) { $success = $false }
    if (-not (New-NotificationsTable)) { $success = $false }
    
    if ($success) {
        Write-Status "All DynamoDB tables created successfully!"
    }
    else {
        Write-Error "Some tables failed to create"
        exit 1
    }
}

# Run main function
try {
    Main
}
catch {
    Write-Error "Table creation failed: $_"
    exit 1
}
