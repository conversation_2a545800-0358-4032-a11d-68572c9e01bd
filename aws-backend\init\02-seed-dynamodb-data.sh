#!/bin/bash

# GameFlex DynamoDB Data Seeding Script
# This script seeds DynamoDB tables with initial development data
# Runs automatically after table creation

set -e

echo "[DYNAMODB] Seeding DynamoDB tables with development data..."

# AWS CLI configuration for LocalStack
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_DEFAULT_REGION=us-east-1
ENDPOINT_URL="http://localhost:4566"

# Function to put item in DynamoDB
put_item() {
    local table_name=$1
    local item_json=$2
    
    echo "$item_json" | aws --endpoint-url=$ENDPOINT_URL dynamodb put-item --table-name "$table_name" --item file:///dev/stdin
    
    if [ $? -eq 0 ]; then
        echo "[INFO] Added item to $table_name"
    else
        echo "[ERROR] Failed to add item to $table_name"
        return 1
    fi
}

# Seed Users table
seed_users() {
    echo "[INFO] Seeding Users table..."
    
    # Developer user
    put_item "Users" '{
        "id": {"S": "550e8400-e29b-41d4-a716-************"},
        "cognito_user_id": {"S": "dev-user-cognito-id-placeholder"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "developer"},
        "display_name": {"S": "GameFlex Developer"},
        "bio": {"S": "Development account for testing GameFlex features"},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
    
    # Admin user
    put_item "Users" '{
        "id": {"S": "550e8400-e29b-41d4-a716-************"},
        "cognito_user_id": {"S": "admin-user-cognito-id-placeholder"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "admin"},
        "display_name": {"S": "GameFlex Admin"},
        "bio": {"S": "Administrator account with full access"},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
    
    # Alice user
    put_item "Users" '{
        "id": {"S": "550e8400-e29b-41d4-a716-************"},
        "cognito_user_id": {"S": "alice-user-cognito-id-placeholder"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "alice_gamer"},
        "display_name": {"S": "Alice Cooper"},
        "bio": {"S": "Passionate gamer and content creator"},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
    
    # Bob user
    put_item "Users" '{
        "id": {"S": "550e8400-e29b-41d4-a716-************"},
        "cognito_user_id": {"S": "bob-user-cognito-id-placeholder"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "bob_streamer"},
        "display_name": {"S": "Bob Wilson"},
        "bio": {"S": "Professional esports player and streamer"},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
    
    # Charlie user
    put_item "Users" '{
        "id": {"S": "550e8400-e29b-41d4-a716-************"},
        "cognito_user_id": {"S": "charlie-user-cognito-id-placeholder"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "charlie_dev"},
        "display_name": {"S": "Charlie Brown"},
        "bio": {"S": "Game developer and indie creator"},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
}

# Seed UserProfiles table
seed_user_profiles() {
    echo "[INFO] Seeding UserProfiles table..."
    
    put_item "UserProfiles" '{
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "first_name": {"S": "Dev"},
        "last_name": {"S": "User"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/New_York"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
    
    put_item "UserProfiles" '{
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "first_name": {"S": "Admin"},
        "last_name": {"S": "User"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/Los_Angeles"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
    
    put_item "UserProfiles" '{
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "first_name": {"S": "Alice"},
        "last_name": {"S": "Cooper"},
        "country": {"S": "Canada"},
        "timezone": {"S": "America/Toronto"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
    
    put_item "UserProfiles" '{
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "first_name": {"S": "Bob"},
        "last_name": {"S": "Wilson"},
        "country": {"S": "United Kingdom"},
        "timezone": {"S": "Europe/London"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
    
    put_item "UserProfiles" '{
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "first_name": {"S": "Charlie"},
        "last_name": {"S": "Brown"},
        "country": {"S": "Australia"},
        "timezone": {"S": "Australia/Sydney"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
}

# Seed Channels table
seed_channels() {
    echo "[INFO] Seeding Channels table..."
    
    put_item "Channels" '{
        "id": {"S": "660e8400-e29b-41d4-a716-************"},
        "name": {"S": "Fortnite"},
        "description": {"S": "Everything about Fortnite - tips, tricks, and epic moments"},
        "owner_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "is_public": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "member_count": {"N": "150"},
        "post_count": {"N": "45"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
    
    put_item "Channels" '{
        "id": {"S": "660e8400-e29b-41d4-a716-************"},
        "name": {"S": "Valorant"},
        "description": {"S": "Tactical FPS discussions, strategies, and highlights"},
        "owner_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "is_public": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "member_count": {"N": "200"},
        "post_count": {"N": "67"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
    
    put_item "Channels" '{
        "id": {"S": "660e8400-e29b-41d4-a716-************"},
        "name": {"S": "Minecraft"},
        "description": {"S": "Creative builds, survival tips, and community projects"},
        "owner_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "is_public": {"BOOL": true},
        "is_verified": {"BOOL": false},
        "member_count": {"N": "300"},
        "post_count": {"N": "89"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
    
    put_item "Channels" '{
        "id": {"S": "660e8400-e29b-41d4-a716-************"},
        "name": {"S": "League of Legends"},
        "description": {"S": "MOBA strategies, champion guides, and esports news"},
        "owner_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "is_public": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "member_count": {"N": "500"},
        "post_count": {"N": "123"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
    
    put_item "Channels" '{
        "id": {"S": "660e8400-e29b-41d4-a716-************"},
        "name": {"S": "Indie Games"},
        "description": {"S": "Discover and discuss amazing indie games"},
        "owner_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "is_public": {"BOOL": true},
        "is_verified": {"BOOL": false},
        "member_count": {"N": "75"},
        "post_count": {"N": "34"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
}

# Main execution
echo "[DYNAMODB] Starting data seeding..."

seed_users
seed_user_profiles
seed_channels

echo "[DYNAMODB] Basic data seeded successfully!"
echo "[INFO] Additional data (channel members, media, posts, etc.) will be added by Lambda functions during runtime"
