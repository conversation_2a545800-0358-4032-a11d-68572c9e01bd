#!/bin/bash

# GameFlex DynamoDB Tables Creation Script
# This script creates all DynamoDB tables for the GameFlex application
# Runs automatically when LocalStack starts

set -e

echo "[DYNAMODB] Creating DynamoDB tables for GameFlex..."

# AWS CLI configuration for LocalStack
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_DEFAULT_REGION=us-east-1
ENDPOINT_URL="http://localhost:4566"

# Function to create a DynamoDB table
create_table() {
    local table_name=$1
    local table_definition=$2
    
    echo "[INFO] Creating table: $table_name"
    
    # Check if table exists
    if aws --endpoint-url=$ENDPOINT_URL dynamodb describe-table --table-name "$table_name" >/dev/null 2>&1; then
        echo "[INFO] Table $table_name already exists"
        return 0
    fi
    
    # Create table
    echo "$table_definition" | aws --endpoint-url=$ENDPOINT_URL dynamodb create-table --cli-input-json file:///dev/stdin
    
    if [ $? -eq 0 ]; then
        echo "[INFO] Created DynamoDB table: $table_name"
        
        # Wait for table to be active
        echo "[INFO] Waiting for table $table_name to be active..."
        aws --endpoint-url=$ENDPOINT_URL dynamodb wait table-exists --table-name "$table_name"
        echo "[INFO] Table $table_name is now active"
        return 0
    else
        echo "[ERROR] Failed to create table $table_name"
        return 1
    fi
}

# Create Users table
create_users_table() {
    local table_definition='{
        "TableName": "Users",
        "AttributeDefinitions": [
            {"AttributeName": "id", "AttributeType": "S"},
            {"AttributeName": "cognito_user_id", "AttributeType": "S"},
            {"AttributeName": "email", "AttributeType": "S"}
        ],
        "KeySchema": [
            {"AttributeName": "id", "KeyType": "HASH"}
        ],
        "GlobalSecondaryIndexes": [
            {
                "IndexName": "CognitoUserIdIndex",
                "KeySchema": [
                    {"AttributeName": "cognito_user_id", "KeyType": "HASH"}
                ],
                "Projection": {"ProjectionType": "ALL"},
                "BillingMode": "PAY_PER_REQUEST"
            },
            {
                "IndexName": "EmailIndex",
                "KeySchema": [
                    {"AttributeName": "email", "KeyType": "HASH"}
                ],
                "Projection": {"ProjectionType": "ALL"},
                "BillingMode": "PAY_PER_REQUEST"
            }
        ],
        "BillingMode": "PAY_PER_REQUEST"
    }'
    
    create_table "Users" "$table_definition"
}

# Create UserProfiles table
create_user_profiles_table() {
    local table_definition='{
        "TableName": "UserProfiles",
        "AttributeDefinitions": [
            {"AttributeName": "user_id", "AttributeType": "S"}
        ],
        "KeySchema": [
            {"AttributeName": "user_id", "KeyType": "HASH"}
        ],
        "BillingMode": "PAY_PER_REQUEST"
    }'
    
    create_table "UserProfiles" "$table_definition"
}

# Create Channels table
create_channels_table() {
    local table_definition='{
        "TableName": "Channels",
        "AttributeDefinitions": [
            {"AttributeName": "id", "AttributeType": "S"},
            {"AttributeName": "owner_id", "AttributeType": "S"}
        ],
        "KeySchema": [
            {"AttributeName": "id", "KeyType": "HASH"}
        ],
        "GlobalSecondaryIndexes": [
            {
                "IndexName": "OwnerIdIndex",
                "KeySchema": [
                    {"AttributeName": "owner_id", "KeyType": "HASH"}
                ],
                "Projection": {"ProjectionType": "ALL"},
                "BillingMode": "PAY_PER_REQUEST"
            }
        ],
        "BillingMode": "PAY_PER_REQUEST"
    }'
    
    create_table "Channels" "$table_definition"
}

# Create ChannelMembers table
create_channel_members_table() {
    local table_definition='{
        "TableName": "ChannelMembers",
        "AttributeDefinitions": [
            {"AttributeName": "channel_id", "AttributeType": "S"},
            {"AttributeName": "user_id", "AttributeType": "S"}
        ],
        "KeySchema": [
            {"AttributeName": "channel_id", "KeyType": "HASH"},
            {"AttributeName": "user_id", "KeyType": "RANGE"}
        ],
        "GlobalSecondaryIndexes": [
            {
                "IndexName": "UserIdIndex",
                "KeySchema": [
                    {"AttributeName": "user_id", "KeyType": "HASH"},
                    {"AttributeName": "channel_id", "KeyType": "RANGE"}
                ],
                "Projection": {"ProjectionType": "ALL"},
                "BillingMode": "PAY_PER_REQUEST"
            }
        ],
        "BillingMode": "PAY_PER_REQUEST"
    }'
    
    create_table "ChannelMembers" "$table_definition"
}

# Create Media table
create_media_table() {
    local table_definition='{
        "TableName": "Media",
        "AttributeDefinitions": [
            {"AttributeName": "id", "AttributeType": "S"},
            {"AttributeName": "owner_id", "AttributeType": "S"},
            {"AttributeName": "channel_id", "AttributeType": "S"}
        ],
        "KeySchema": [
            {"AttributeName": "id", "KeyType": "HASH"}
        ],
        "GlobalSecondaryIndexes": [
            {
                "IndexName": "OwnerIdIndex",
                "KeySchema": [
                    {"AttributeName": "owner_id", "KeyType": "HASH"}
                ],
                "Projection": {"ProjectionType": "ALL"},
                "BillingMode": "PAY_PER_REQUEST"
            },
            {
                "IndexName": "ChannelIdIndex",
                "KeySchema": [
                    {"AttributeName": "channel_id", "KeyType": "HASH"}
                ],
                "Projection": {"ProjectionType": "ALL"},
                "BillingMode": "PAY_PER_REQUEST"
            }
        ],
        "BillingMode": "PAY_PER_REQUEST"
    }'
    
    create_table "Media" "$table_definition"
}

# Create Posts table
create_posts_table() {
    local table_definition='{
        "TableName": "Posts",
        "AttributeDefinitions": [
            {"AttributeName": "id", "AttributeType": "S"},
            {"AttributeName": "author_id", "AttributeType": "S"},
            {"AttributeName": "channel_id", "AttributeType": "S"},
            {"AttributeName": "created_at", "AttributeType": "S"}
        ],
        "KeySchema": [
            {"AttributeName": "id", "KeyType": "HASH"}
        ],
        "GlobalSecondaryIndexes": [
            {
                "IndexName": "AuthorIdCreatedAtIndex",
                "KeySchema": [
                    {"AttributeName": "author_id", "KeyType": "HASH"},
                    {"AttributeName": "created_at", "KeyType": "RANGE"}
                ],
                "Projection": {"ProjectionType": "ALL"},
                "BillingMode": "PAY_PER_REQUEST"
            },
            {
                "IndexName": "ChannelIdCreatedAtIndex",
                "KeySchema": [
                    {"AttributeName": "channel_id", "KeyType": "HASH"},
                    {"AttributeName": "created_at", "KeyType": "RANGE"}
                ],
                "Projection": {"ProjectionType": "ALL"},
                "BillingMode": "PAY_PER_REQUEST"
            }
        ],
        "BillingMode": "PAY_PER_REQUEST"
    }'
    
    create_table "Posts" "$table_definition"
}

# Create Comments table
create_comments_table() {
    local table_definition='{
        "TableName": "Comments",
        "AttributeDefinitions": [
            {"AttributeName": "post_id", "AttributeType": "S"},
            {"AttributeName": "id", "AttributeType": "S"},
            {"AttributeName": "author_id", "AttributeType": "S"},
            {"AttributeName": "created_at", "AttributeType": "S"}
        ],
        "KeySchema": [
            {"AttributeName": "post_id", "KeyType": "HASH"},
            {"AttributeName": "id", "KeyType": "RANGE"}
        ],
        "GlobalSecondaryIndexes": [
            {
                "IndexName": "AuthorIdCreatedAtIndex",
                "KeySchema": [
                    {"AttributeName": "author_id", "KeyType": "HASH"},
                    {"AttributeName": "created_at", "KeyType": "RANGE"}
                ],
                "Projection": {"ProjectionType": "ALL"},
                "BillingMode": "PAY_PER_REQUEST"
            }
        ],
        "BillingMode": "PAY_PER_REQUEST"
    }'
    
    create_table "Comments" "$table_definition"
}

# Create Likes table
create_likes_table() {
    local table_definition='{
        "TableName": "Likes",
        "AttributeDefinitions": [
            {"AttributeName": "post_id", "AttributeType": "S"},
            {"AttributeName": "user_id", "AttributeType": "S"}
        ],
        "KeySchema": [
            {"AttributeName": "post_id", "KeyType": "HASH"},
            {"AttributeName": "user_id", "KeyType": "RANGE"}
        ],
        "BillingMode": "PAY_PER_REQUEST"
    }'
    
    create_table "Likes" "$table_definition"
}

# Create Follows table
create_follows_table() {
    local table_definition='{
        "TableName": "Follows",
        "AttributeDefinitions": [
            {"AttributeName": "follower_id", "AttributeType": "S"},
            {"AttributeName": "following_id", "AttributeType": "S"}
        ],
        "KeySchema": [
            {"AttributeName": "follower_id", "KeyType": "HASH"},
            {"AttributeName": "following_id", "KeyType": "RANGE"}
        ],
        "GlobalSecondaryIndexes": [
            {
                "IndexName": "FollowingIdIndex",
                "KeySchema": [
                    {"AttributeName": "following_id", "KeyType": "HASH"},
                    {"AttributeName": "follower_id", "KeyType": "RANGE"}
                ],
                "Projection": {"ProjectionType": "ALL"},
                "BillingMode": "PAY_PER_REQUEST"
            }
        ],
        "BillingMode": "PAY_PER_REQUEST"
    }'
    
    create_table "Follows" "$table_definition"
}

# Create Notifications table
create_notifications_table() {
    local table_definition='{
        "TableName": "Notifications",
        "AttributeDefinitions": [
            {"AttributeName": "user_id", "AttributeType": "S"},
            {"AttributeName": "created_at", "AttributeType": "S"}
        ],
        "KeySchema": [
            {"AttributeName": "user_id", "KeyType": "HASH"},
            {"AttributeName": "created_at", "KeyType": "RANGE"}
        ],
        "BillingMode": "PAY_PER_REQUEST"
    }'
    
    create_table "Notifications" "$table_definition"
}

# Main execution
echo "[DYNAMODB] Starting table creation..."

create_users_table
create_user_profiles_table
create_channels_table
create_channel_members_table
create_media_table
create_posts_table
create_comments_table
create_likes_table
create_follows_table
create_notifications_table

echo "[DYNAMODB] All DynamoDB tables created successfully!"
