# GameFlex AWS Services Initialization Script (PowerShell)
# This script initializes AWS services in LocalStack

param(
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

# AWS CLI configuration for LocalStack
$env:AWS_ACCESS_KEY_ID = "test"
$env:AWS_SECRET_ACCESS_KEY = "test"
$env:AWS_DEFAULT_REGION = "us-east-1"
$ENDPOINT_URL = "http://localhost:45660"

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "[AWS-INIT] $Message" -ForegroundColor Blue
}

# Test AWS CLI and LocalStack connectivity
function Test-AwsConnection {
    try {
        $result = aws --endpoint-url=$ENDPOINT_URL sts get-caller-identity 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Status "AWS CLI connection to LocalStack successful"
            return $true
        }
        else {
            Write-Error "Failed to connect to LocalStack"
            return $false
        }
    }
    catch {
        Write-Error "AWS CLI not available or LocalStack not accessible: $_"
        return $false
    }
}

# Create S3 buckets
function New-S3Buckets {
    Write-Status "Creating S3 buckets..."
    
    $buckets = @(
        "gameflex-media",
        "gameflex-avatars", 
        "gameflex-temp"
    )
    
    foreach ($bucket in $buckets) {
        try {
            # Check if bucket exists
            $exists = aws --endpoint-url=$ENDPOINT_URL s3api head-bucket --bucket $bucket 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Status "Bucket $bucket already exists"
            }
            else {
                # Create bucket
                aws --endpoint-url=$ENDPOINT_URL s3api create-bucket --bucket $bucket | Out-Null
                Write-Status "Created S3 bucket: $bucket"
                
                # Set bucket policy for public read access (development only)
                $policy = @{
                    Version   = "2012-10-17"
                    Statement = @(
                        @{
                            Sid       = "PublicReadGetObject"
                            Effect    = "Allow"
                            Principal = "*"
                            Action    = "s3:GetObject"
                            Resource  = "arn:aws:s3:::$bucket/*"
                        }
                    )
                } | ConvertTo-Json -Depth 10
                
                $policy | Out-File -FilePath "temp-policy.json" -Encoding UTF8
                aws --endpoint-url=$ENDPOINT_URL s3api put-bucket-policy --bucket $bucket --policy file://temp-policy.json | Out-Null
                Remove-Item "temp-policy.json" -Force
                
                Write-Status "Set public read policy for bucket: $bucket"
            }
        }
        catch {
            Write-Error "Failed to create bucket $bucket : $_"
        }
    }
}

# Verify DynamoDB tables
function Test-DynamoDBTables {
    Write-Status "Verifying DynamoDB tables..."

    $tables = @("Users", "UserProfiles", "Channels", "ChannelMembers", "Media", "Posts", "Comments", "Likes", "Follows", "Notifications")
    $allTablesExist = $true

    foreach ($table in $tables) {
        try {
            $result = aws --endpoint-url=$ENDPOINT_URL dynamodb describe-table --table-name $table 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Status "DynamoDB table exists: $table"
            }
            else {
                Write-Warning "DynamoDB table missing: $table"
                $allTablesExist = $false
            }
        }
        catch {
            Write-Warning "Failed to check DynamoDB table: $table"
            $allTablesExist = $false
        }
    }

    if ($allTablesExist) {
        Write-Status "All DynamoDB tables are available"
    }
    else {
        Write-Warning "Some DynamoDB tables are missing - they should be created by init scripts"
    }

    return $true
}

# Create Cognito User Pool
function New-CognitoUserPool {
    Write-Status "Creating Cognito User Pool..."
    
    try {
        # Check if user pool exists
        $existingPools = aws --endpoint-url=$ENDPOINT_URL cognito-idp list-user-pools --max-results 10 --query "UserPools[?Name=='gameflex-users'].Id" --output text 2>$null
        
        if ($existingPools -and $existingPools.Trim() -ne "") {
            $userPoolId = $existingPools.Trim()
            Write-Status "User pool already exists with ID: $userPoolId"
        }
        else {
            # Create user pool
            $userPoolConfig = @{
                PoolName               = "gameflex-users"
                Policies               = @{
                    PasswordPolicy = @{
                        MinimumLength    = 8
                        RequireUppercase = $true
                        RequireLowercase = $true
                        RequireNumbers   = $true
                        RequireSymbols   = $false
                    }
                }
                AutoVerifiedAttributes = @("email")
                UsernameAttributes     = @("email")
                Schema                 = @(
                    @{
                        Name              = "email"
                        AttributeDataType = "String"
                        Required          = $true
                        Mutable           = $true
                    },
                    @{
                        Name              = "given_name"
                        AttributeDataType = "String"
                        Required          = $false
                        Mutable           = $true
                    },
                    @{
                        Name              = "family_name"
                        AttributeDataType = "String"
                        Required          = $false
                        Mutable           = $true
                    }
                )
            } | ConvertTo-Json -Depth 10
            
            $userPoolConfig | Out-File -FilePath "temp-user-pool.json" -Encoding UTF8
            $result = aws --endpoint-url=$ENDPOINT_URL cognito-idp create-user-pool --cli-input-json file://temp-user-pool.json --output json
            Remove-Item "temp-user-pool.json" -Force
            
            $userPoolData = $result | ConvertFrom-Json
            $userPoolId = $userPoolData.UserPool.Id
            Write-Status "Created Cognito User Pool with ID: $userPoolId"
        }
        
        # Create user pool client
        $existingClients = aws --endpoint-url=$ENDPOINT_URL cognito-idp list-user-pool-clients --user-pool-id $userPoolId --query "UserPoolClients[?ClientName=='gameflex-client'].ClientId" --output text 2>$null
        
        if ($existingClients -and $existingClients.Trim() -ne "") {
            $clientId = $existingClients.Trim()
            Write-Status "User pool client already exists with ID: $clientId"
        }
        else {
            $clientResult = aws --endpoint-url=$ENDPOINT_URL cognito-idp create-user-pool-client `
                --user-pool-id $userPoolId `
                --client-name "gameflex-client" `
                --generate-secret `
                --explicit-auth-flows "ADMIN_NO_SRP_AUTH" "ALLOW_USER_PASSWORD_AUTH" "ALLOW_REFRESH_TOKEN_AUTH" `
                --output json
            
            $clientData = $clientResult | ConvertFrom-Json
            $clientId = $clientData.UserPoolClient.ClientId
            Write-Status "Created User Pool Client with ID: $clientId"
        }
        
        # Update .env file with Cognito IDs
        Update-EnvFile -UserPoolId $userPoolId -ClientId $clientId
        
        return @{
            UserPoolId = $userPoolId
            ClientId   = $clientId
        }
    }
    catch {
        Write-Error "Failed to create Cognito User Pool: $_"
        return $null
    }
}

# Create Cognito Identity Pool
function New-CognitoIdentityPool {
    param(
        [string]$UserPoolId,
        [string]$ClientId
    )
    
    Write-Status "Creating Cognito Identity Pool..."
    
    try {
        # Check if identity pool exists
        $existingPools = aws --endpoint-url=$ENDPOINT_URL cognito-identity list-identity-pools --max-results 10 --query "IdentityPools[?IdentityPoolName=='gameflex-identity'].IdentityPoolId" --output text 2>$null
        
        if ($existingPools -and $existingPools.Trim() -ne "") {
            $identityPoolId = $existingPools.Trim()
            Write-Status "Identity pool already exists with ID: $identityPoolId"
        }
        else {
            $identityPoolConfig = @{
                IdentityPoolName               = "gameflex-identity"
                AllowUnauthenticatedIdentities = $false
                CognitoIdentityProviders       = @(
                    @{
                        ProviderName         = "cognito-idp.us-east-1.amazonaws.com/$UserPoolId"
                        ClientId             = $ClientId
                        ServerSideTokenCheck = $false
                    }
                )
            } | ConvertTo-Json -Depth 10
            
            $identityPoolConfig | Out-File -FilePath "temp-identity-pool.json" -Encoding UTF8
            $result = aws --endpoint-url=$ENDPOINT_URL cognito-identity create-identity-pool --cli-input-json file://temp-identity-pool.json --output json
            Remove-Item "temp-identity-pool.json" -Force
            
            $identityPoolData = $result | ConvertFrom-Json
            $identityPoolId = $identityPoolData.IdentityPoolId
            Write-Status "Created Cognito Identity Pool with ID: $identityPoolId"
        }
        
        return $identityPoolId
    }
    catch {
        Write-Error "Failed to create Cognito Identity Pool: $_"
        return $null
    }
}

# Update .env file with generated IDs
function Update-EnvFile {
    param(
        [string]$UserPoolId,
        [string]$ClientId
    )
    
    try {
        $envPath = ".env"
        if (Test-Path $envPath) {
            $content = Get-Content $envPath
            
            # Update or add Cognito configuration
            $content = $content | ForEach-Object {
                if ($_ -match "^COGNITO_USER_POOL_ID=") {
                    "COGNITO_USER_POOL_ID=$UserPoolId"
                }
                elseif ($_ -match "^COGNITO_USER_POOL_CLIENT_ID=") {
                    "COGNITO_USER_POOL_CLIENT_ID=$ClientId"
                }
                else {
                    $_
                }
            }
            
            $content | Set-Content $envPath
            Write-Status "Updated .env file with Cognito configuration"
        }
    }
    catch {
        Write-Warning "Failed to update .env file: $_"
    }
}

# Main execution
function Main {
    Write-Header "Initializing AWS services in LocalStack..."
    Write-Host ""
    
    if (-not (Test-AwsConnection)) {
        Write-Error "Cannot connect to LocalStack. Make sure it's running."
        exit 1
    }

    New-S3Buckets
    Test-DynamoDBTables

    $cognitoResult = New-CognitoUserPool
    if ($cognitoResult) {
        $identityPoolId = New-CognitoIdentityPool -UserPoolId $cognitoResult.UserPoolId -ClientId $cognitoResult.ClientId
        
        Write-Host ""
        Write-Status "AWS Services initialized successfully!"
        Write-Host ""
        Write-Status "Cognito Configuration:"
        Write-Host "  User Pool ID: $($cognitoResult.UserPoolId)" -ForegroundColor Cyan
        Write-Host "  Client ID: $($cognitoResult.ClientId)" -ForegroundColor Cyan
        Write-Host "  Identity Pool ID: $identityPoolId" -ForegroundColor Cyan
    }
    else {
        Write-Error "Failed to initialize Cognito services"
        exit 1
    }
    
    Write-Status "Initialization completed!"
}

# Run main function
try {
    Main
}
catch {
    Write-Error "Initialization failed: $_"
    exit 1
}
