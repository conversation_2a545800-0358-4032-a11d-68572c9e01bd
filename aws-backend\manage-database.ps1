# GameFlex DynamoDB Management Script (PowerShell)
# This script provides DynamoDB table management utilities for the AWS backend

param(
    [string]$Action = "status",
    [string]$EndpointUrl = "http://localhost:45660",
    [switch]$Force,
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "[DB] $Message" -ForegroundColor Blue
}

# Test DynamoDB connection
function Test-DynamoDBConnection {
    try {
        # Set AWS credentials for LocalStack
        $env:AWS_ACCESS_KEY_ID = "test"
        $env:AWS_SECRET_ACCESS_KEY = "test"
        $env:AWS_DEFAULT_REGION = "us-east-1"

        $result = aws --endpoint-url=$EndpointUrl dynamodb list-tables 2>$null

        if ($LASTEXITCODE -eq 0) {
            Write-Status "DynamoDB connection successful"
            return $true
        }
        else {
            Write-Error "DynamoDB connection failed"
            return $false
        }
    }
    catch {
        Write-Error "Failed to test DynamoDB connection: $_"
        return $false
    }
    finally {
        Remove-Item Env:AWS_ACCESS_KEY_ID -ErrorAction SilentlyContinue
        Remove-Item Env:AWS_SECRET_ACCESS_KEY -ErrorAction SilentlyContinue
        Remove-Item Env:AWS_DEFAULT_REGION -ErrorAction SilentlyContinue
    }
}

# Get database status
function Get-DatabaseStatus {
    try {
        $env:PGPASSWORD = $Password
        
        Write-Header "Database Status"
        Write-Host ""
        
        # Connection info
        Write-Status "Connection Information:"
        Write-Host "  Host: $DbHost" -ForegroundColor Cyan
        Write-Host "  Port: $Port" -ForegroundColor Cyan
        Write-Host "  Database: $Database" -ForegroundColor Cyan
        Write-Host "  Username: $Username" -ForegroundColor Cyan
        Write-Host ""
        
        # Database size
        $sizeQuery = "SELECT pg_size_pretty(pg_database_size('$Database')) as size;"
        $size = psql -h $DbHost -p $Port -U $Username -d $Database -t -c $sizeQuery 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Status "Database Size: $($size.Trim())"
        }
        
        # Table counts
        Write-Status "Table Row Counts:"
        $tables = @("users", "user_profiles", "channels", "channel_members", "media", "posts", "comments", "likes", "follows", "notifications")
        
        foreach ($table in $tables) {
            $countQuery = "SELECT COUNT(*) FROM $table;"
            $count = psql -h $DbHost -p $Port -U $Username -d $Database -t -c $countQuery 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "  $table : $($count.Trim())" -ForegroundColor Cyan
            }
        }
        
        Write-Host ""
        
        # Recent activity
        Write-Status "Recent Posts (Last 5):"
        $recentQuery = @"
SELECT 
    p.content,
    u.username,
    c.name as channel,
    p.created_at
FROM posts p
JOIN users u ON p.author_id = u.id
LEFT JOIN channels c ON p.channel_id = c.id
ORDER BY p.created_at DESC
LIMIT 5;
"@
        
        psql -h $DbHost -p $Port -U $Username -d $Database -c $recentQuery 2>$null
        
        return $true
    }
    catch {
        Write-Error "Failed to get database status: $_"
        return $false
    }
    finally {
        Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
    }
}

# Reset database
function Reset-Database {
    if (-not $Force) {
        $response = Read-Host "This will delete ALL data in the database. Are you sure? (type 'yes' to confirm)"
        if ($response -ne "yes") {
            Write-Status "Database reset cancelled"
            return $true
        }
    }
    
    try {
        $env:PGPASSWORD = $Password
        
        Write-Header "Resetting Database"
        
        # Drop all tables
        Write-Status "Dropping existing tables..."
        $dropQuery = @"
DROP TABLE IF EXISTS notifications CASCADE;
DROP TABLE IF EXISTS follows CASCADE;
DROP TABLE IF EXISTS likes CASCADE;
DROP TABLE IF EXISTS comments CASCADE;
DROP TABLE IF EXISTS posts CASCADE;
DROP TABLE IF EXISTS media CASCADE;
DROP TABLE IF EXISTS channel_members CASCADE;
DROP TABLE IF EXISTS channels CASCADE;
DROP TABLE IF EXISTS user_profiles CASCADE;
DROP TABLE IF EXISTS users CASCADE;
"@
        
        psql -h $DbHost -p $Port -U $Username -d $Database -c $dropQuery
        
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to drop tables"
        }
        
        Write-Status "Tables dropped successfully"
        
        # Recreate schema
        Write-Status "Recreating database schema..."
        psql -h $DbHost -p $Port -U $Username -d $Database -f "database\init\01-init-schema.sql"
        
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to create schema"
        }
        
        Write-Status "Schema created successfully"
        
        # Insert seed data
        Write-Status "Inserting seed data..."
        psql -h $DbHost -p $Port -U $Username -d $Database -f "database\init\02-seed-data.sql"
        
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to insert seed data"
        }
        
        Write-Status "Seed data inserted successfully"
        Write-Status "Database reset completed!"
        
        return $true
    }
    catch {
        Write-Error "Failed to reset database: $_"
        return $false
    }
    finally {
        Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
    }
}

# Backup database
function Backup-Database {
    param([string]$BackupPath)
    
    if (-not $BackupPath) {
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $BackupPath = "backup_${Database}_${timestamp}.sql"
    }
    
    try {
        $env:PGPASSWORD = $Password
        
        Write-Header "Creating Database Backup"
        Write-Status "Backup file: $BackupPath"
        
        pg_dump -h $DbHost -p $Port -U $Username -d $Database -f $BackupPath
        
        if ($LASTEXITCODE -eq 0) {
            Write-Status "Database backup created successfully"
            $size = (Get-Item $BackupPath).Length
            Write-Status "Backup size: $([math]::Round($size / 1MB, 2)) MB"
            return $true
        }
        else {
            Write-Error "Database backup failed"
            return $false
        }
    }
    catch {
        Write-Error "Failed to create database backup: $_"
        return $false
    }
    finally {
        Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
    }
}

# Restore database
function Restore-Database {
    param([string]$BackupPath)
    
    if (-not $BackupPath -or -not (Test-Path $BackupPath)) {
        Write-Error "Backup file not found: $BackupPath"
        return $false
    }
    
    if (-not $Force) {
        $response = Read-Host "This will replace ALL data in the database. Are you sure? (type 'yes' to confirm)"
        if ($response -ne "yes") {
            Write-Status "Database restore cancelled"
            return $true
        }
    }
    
    try {
        $env:PGPASSWORD = $Password
        
        Write-Header "Restoring Database"
        Write-Status "Restore file: $BackupPath"
        
        psql -h $DbHost -p $Port -U $Username -d $Database -f $BackupPath
        
        if ($LASTEXITCODE -eq 0) {
            Write-Status "Database restored successfully"
            return $true
        }
        else {
            Write-Error "Database restore failed"
            return $false
        }
    }
    catch {
        Write-Error "Failed to restore database: $_"
        return $false
    }
    finally {
        Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
    }
}

# Execute custom SQL
function Invoke-CustomSql {
    param([string]$SqlFile)
    
    if (-not $SqlFile -or -not (Test-Path $SqlFile)) {
        Write-Error "SQL file not found: $SqlFile"
        return $false
    }
    
    try {
        $env:PGPASSWORD = $Password
        
        Write-Header "Executing SQL File"
        Write-Status "SQL file: $SqlFile"
        
        psql -h $DbHost -p $Port -U $Username -d $Database -f $SqlFile
        
        if ($LASTEXITCODE -eq 0) {
            Write-Status "SQL executed successfully"
            return $true
        }
        else {
            Write-Error "SQL execution failed"
            return $false
        }
    }
    catch {
        Write-Error "Failed to execute SQL: $_"
        return $false
    }
    finally {
        Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
    }
}

# Show DynamoDB status
function Show-DynamoDBStatus {
    Write-Header "DynamoDB Status"

    if (-not (Test-DynamoDBConnection)) {
        Write-Error "Cannot connect to DynamoDB"
        return $false
    }

    try {
        # Set AWS credentials for LocalStack
        $env:AWS_ACCESS_KEY_ID = "test"
        $env:AWS_SECRET_ACCESS_KEY = "test"
        $env:AWS_DEFAULT_REGION = "us-east-1"

        # DynamoDB info
        Write-Status "DynamoDB Information:"
        Write-Host "  Endpoint: $EndpointUrl" -ForegroundColor Cyan
        Write-Host "  Region: us-east-1" -ForegroundColor Cyan
        Write-Host ""

        # Table counts
        Write-Status "Table Item Counts:"
        $tables = @("Users", "UserProfiles", "Channels", "ChannelMembers", "Media", "Posts", "Comments", "Likes", "Follows", "Notifications")

        foreach ($table in $tables) {
            $scanResult = aws --endpoint-url=$EndpointUrl dynamodb scan --table-name $table --select "COUNT" --output json 2>$null
            if ($LASTEXITCODE -eq 0) {
                $scanData = $scanResult | ConvertFrom-Json
                $count = $scanData.Count
                Write-Host "  $table : $count" -ForegroundColor Cyan
            }
            else {
                Write-Host "  $table : Table not found" -ForegroundColor Red
            }
        }

        Write-Host ""
        return $true
    }
    catch {
        Write-Error "Failed to get DynamoDB status: $_"
        return $false
    }
    finally {
        Remove-Item Env:AWS_ACCESS_KEY_ID -ErrorAction SilentlyContinue
        Remove-Item Env:AWS_SECRET_ACCESS_KEY -ErrorAction SilentlyContinue
        Remove-Item Env:AWS_DEFAULT_REGION -ErrorAction SilentlyContinue
    }
}

# Main function
function Main {
    # Check if AWS CLI is available
    try {
        aws --version | Out-Null
    }
    catch {
        Write-Error "AWS CLI not found. Please install AWS CLI."
        exit 1
    }

    # Test connection first
    if (-not (Test-DynamoDBConnection)) {
        Write-Error "Cannot connect to DynamoDB. Please check LocalStack is running."
        exit 1
    }

    # Execute action
    switch ($Action.ToLower()) {
        "status" {
            Show-DynamoDBStatus
        }
        default {
            Write-Error "Unknown action: $Action"
            Write-Host "Available actions: status"
            exit 1
        }
    }
}

# Show help if requested
if ($args -contains "-h" -or $args -contains "--help") {
    Write-Host "GameFlex Database Management Script" -ForegroundColor Blue
    Write-Host ""
    Write-Host "Usage: .\manage-database.ps1 [ACTION] [OPTIONS]" -ForegroundColor Green
    Write-Host ""
    Write-Host "Actions:" -ForegroundColor Yellow
    Write-Host "  status                Show database status and statistics"
    Write-Host "  reset                 Reset database (drop all tables and recreate)"
    Write-Host "  backup [file]         Create database backup"
    Write-Host "  restore [file]        Restore database from backup"
    Write-Host "  sql [file]            Execute SQL file"
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -DbHost               Database host (default: localhost)"
    Write-Host "  -Port                 Database port (default: 5433)"
    Write-Host "  -Database             Database name (default: gameflex)"
    Write-Host "  -Username             Database username (default: postgres)"
    Write-Host "  -Password             Database password (default: gameflex_password)"
    Write-Host "  -Force                Skip confirmation prompts"
    Write-Host "  -Verbose              Show verbose output"
    Write-Host "  -h, --help            Show this help message"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\manage-database.ps1 status"
    Write-Host "  .\manage-database.ps1 reset -Force"
    Write-Host "  .\manage-database.ps1 backup my_backup.sql"
    Write-Host "  .\manage-database.ps1 restore my_backup.sql"
    Write-Host "  .\manage-database.ps1 sql custom_script.sql"
    exit 0
}

# Run main function
try {
    Main
}
catch {
    Write-Error "Database management failed: $_"
    exit 1
}
