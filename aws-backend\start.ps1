# GameFlex AWS Backend Startup Script (PowerShell)
# This script starts the AWS backend using LocalStack Pro on Windows

param(
    [switch]$Force,
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Load environment variables from .env file
function Load-EnvFile {
    if (Test-Path ".env") {
        Write-Status "Loading environment variables from .env file..."
        Get-Content ".env" | ForEach-Object {
            if ($_ -match "^\s*([^#][^=]*)\s*=\s*(.*)\s*$") {
                $name = $matches[1].Trim()
                $value = $matches[2].Trim()
                # Remove quotes if present
                $value = $value -replace '^"(.*)"$', '$1'
                $value = $value -replace "^'(.*)'$", '$1'
                [Environment]::SetEnvironmentVariable($name, $value, "Process")
                if ($Verbose) {
                    Write-Host "  Set $name" -ForegroundColor Gray
                }
            }
        }
    }
    else {
        Write-Warning ".env file not found. Using default values."
    }
}

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "[GAMEFLEX] $Message" -ForegroundColor Blue
}

# Check if Docker Desktop is running
function Test-Docker {
    try {
        docker info | Out-Null
        Write-Status "Docker Desktop is running"
        return $true
    }
    catch {
        Write-Error "Docker Desktop is not running. Please start Docker Desktop and try again."
        return $false
    }
}

# Check if required ports are available
function Test-Ports {
    $ports = @(4566, 45660)  # Only LocalStack ports needed
    $portsInUse = @()

    foreach ($port in $ports) {
        $connection = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
        if ($connection) {
            $portsInUse += $port
            Write-Warning "Port $port is already in use"
        }
    }

    if ($portsInUse.Count -gt 0 -and -not $Force) {
        $response = Read-Host "Do you want to continue anyway? (y/N)"
        if ($response -notmatch "^[Yy]$") {
            Write-Error "Startup cancelled"
            return $false
        }
    }

    Write-Status "Port check completed"
    return $true
}

# Create necessary directories
function New-Directories {
    Write-Status "Creating necessary directories..."
    
    $directories = @(
        "init",
        "lambda-functions",
        "cloudformation", 
        "database\init",
        "logs"
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
        }
    }
    
    Write-Status "Directories created"
}

# Start Docker services
function Start-Services {
    Write-Header "Starting GameFlex AWS Backend..."
    
    try {
        # Pull latest images
        Write-Status "Pulling latest Docker images..."
        docker compose pull
        
        # Start services
        Write-Status "Starting services..."
        docker compose up -d
        
        # Wait for services to be healthy
        Write-Status "Waiting for services to be ready..."
        
        # Wait for LocalStack
        Write-Status "Waiting for LocalStack to be ready..."
        $timeout = 120
        $counter = 0
        
        do {
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:45660/_localstack/health" -TimeoutSec 5 -ErrorAction SilentlyContinue
                if ($response.StatusCode -eq 200) {
                    Write-Status "LocalStack is ready"
                    break
                }
            }
            catch {
                # Continue waiting
            }
            
            Start-Sleep -Seconds 2
            $counter += 2
            
            if ($counter -ge $timeout) {
                Write-Error "LocalStack failed to start within $timeout seconds"
                docker compose logs localstack
                throw "LocalStack startup timeout"
            }
        } while ($true)
        
        # Wait for DynamoDB to be available through LocalStack
        Write-Status "Waiting for LocalStack DynamoDB to be ready..."
        Start-Sleep -Seconds 5  # Give LocalStack time to initialize DynamoDB
        
        return $true
    }
    catch {
        Write-Error "Failed to start services: $_"
        return $false
    }
}

# Initialize AWS services
function Initialize-AwsServices {
    Write-Status "Initializing AWS services..."
    
    if (Test-Path "init\init-aws-services.ps1") {
        Write-Status "Running AWS services initialization..."
        & ".\init\init-aws-services.ps1"
    }
    else {
        Write-Warning "AWS services initialization script not found"
        Write-Warning "You may need to run the initialization manually"
    }
}

# Display service information
function Show-ServiceInfo {
    Write-Header "GameFlex AWS Backend (LocalStack Pro) is now running!"
    Write-Host ""

    Write-Status "Service URLs:"
    Write-Host "  🌐 LocalStack Dashboard: http://localhost:45660/_localstack/health" -ForegroundColor Cyan
    Write-Host "  🌐 LocalStack Pro Dashboard: http://localhost:4566/_localstack/health" -ForegroundColor Cyan
    Write-Host "  🔒 LocalStack HTTPS Gateway: https://localhost:443" -ForegroundColor Cyan
    Write-Host "  🗄️  DynamoDB (via LocalStack): http://localhost:4566" -ForegroundColor Cyan
    Write-Host "  📦 S3 Console: http://localhost:45660/_aws/s3" -ForegroundColor Cyan
    Write-Host "  🔐 Cognito Console: http://localhost:45660/_aws/cognito" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Status "Development Credentials:"
    Write-Host "  📧 Developer: <EMAIL> / GameFlex123!" -ForegroundColor Cyan
    Write-Host "  👑 Admin: <EMAIL> / AdminGameFlex123!" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Status "Useful Commands:"
    Write-Host "  📊 Check status: docker compose ps" -ForegroundColor Cyan
    Write-Host "  📋 View logs: docker compose logs -f" -ForegroundColor Cyan
    Write-Host "  🛑 Stop services: .\stop.ps1" -ForegroundColor Cyan
    Write-Host "  🔄 Restart: .\stop.ps1; .\start.ps1" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Warning "This is a development environment. Do not use in production!"
}

# Main execution
function Main {
    Write-Header "GameFlex AWS Backend Startup"
    Write-Host ""

    # Load environment variables from .env file
    Load-EnvFile

    if (-not (Test-Docker)) {
        exit 1
    }
    
    if (-not (Test-Ports)) {
        exit 1
    }
    
    New-Directories
    
    if (-not (Start-Services)) {
        exit 1
    }
    
    Initialize-AwsServices
    Show-ServiceInfo
    
    Write-Status "Startup completed successfully!"
}

# Run main function
try {
    Main
}
catch {
    Write-Error "Startup failed: $_"
    exit 1
}
