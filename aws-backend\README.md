# GameFlex AWS Backend

This directory contains the AWS backend setup for GameFlex using LocalStack Pro for development.

## Architecture Overview

The AWS backend uses the following services (all via LocalStack Pro):
- **AWS Cognito**: User authentication and authorization
- **RDS**: Managed PostgreSQL database clusters
- **AWS Lambda**: Serverless functions for API endpoints
- **API Gateway**: REST API routing and management
- **S3**: Object storage for media files (images, videos)
- **IAM**: Identity and access management
- **Secrets Manager**: Secure credential storage
- **CloudFormation**: Infrastructure as Code

## Quick Start

### Prerequisites
- Docker and Docker Compose installed
- At least 6GB of available RAM
- Ports 4566, 4510-4559, 443 available (LocalStack Pro)
- LocalStack Pro auth token configured
- AWS CLI installed (optional, for testing)

### Start the Backend

**Linux/macOS:**
```bash
cd aws-backend
chmod +x start.sh
./start.sh
```

**Windows (PowerShell):**
```powershell
cd aws-backend
.\start.ps1
```

### Access Services
- **LocalStack Pro Dashboard**: http://localhost:4566/_localstack/health
- **LocalStack HTTPS Gateway**: https://localhost:443
- **API Gateway**: http://localhost:4566/restapis/{api-id}/dev/_user_request_
- **S3 Console**: http://localhost:4566/_aws/s3
- **Cognito Console**: http://localhost:4566/_aws/cognito
- **Compatibility Port**: http://localhost:45660 (for existing scripts)

## Development Configuration

### LocalStack Pro Setup
This backend now uses LocalStack Pro which provides enhanced features and better AWS service compatibility.

**Configuration**:
1. Copy `.env.example` to `.env`
2. Update `LOCALSTACK_AUTH_TOKEN` with your LocalStack Pro auth token
3. Modify other settings as needed

**Getting Your Auth Token**:
- Sign up at https://app.localstack.cloud/
- Copy your auth token from the dashboard
- Add it to the `.env` file

**Pro Features Enabled**:
- Enhanced AWS service emulation
- HTTPS Gateway support
- Extended port range for external services
- Improved persistence and performance

### Environment Variables
Key environment variables (configured in `.env` file):
- `LOCALSTACK_AUTH_TOKEN` - LocalStack Pro auth token (required)
- `AWS_ACCESS_KEY_ID` - LocalStack access key (test)
- `AWS_SECRET_ACCESS_KEY` - LocalStack secret key (test)
- `AWS_DEFAULT_REGION` - AWS region (us-east-1)
- `LOCALSTACK_HOST` - LocalStack host (localhost:4566)
- `LOCALSTACK_DOCKER_NAME` - Docker container name
- `LOCALSTACK_VOLUME_DIR` - Volume directory for persistence

### Default Test Users
- **Developer Account**
  - Email: `<EMAIL>`
  - Password: `GameFlex123!`

- **Admin Account**
  - Email: `<EMAIL>`
  - Password: `AdminGameFlex123!`

## Services

### Core AWS Services
- **Cognito User Pool** - User authentication
- **Cognito Identity Pool** - AWS resource access
- **Aurora Serverless** - PostgreSQL database
- **Lambda Functions** - API business logic
- **API Gateway** - REST API endpoints
- **S3 Buckets** - Media storage

### Database Schema
The Aurora database includes the same tables as the Supabase backend:
- `users` - User accounts
- `user_profiles` - Extended user information
- `channels` - Gaming channels/communities
- `posts` - User posts/content
- `comments` - Post comments
- `likes` - Likes on posts/comments
- `follows` - User follow relationships
- `notifications` - User notifications
- `media` - Media file metadata

## API Endpoints

### Authentication
- `POST /auth/signup` - User registration
- `POST /auth/signin` - User login
- `POST /auth/signout` - User logout
- `POST /auth/refresh` - Refresh tokens

### Users
- `GET /users/profile` - Get user profile
- `PUT /users/profile` - Update user profile
- `POST /users/avatar` - Upload avatar

### Posts
- `GET /posts` - List posts
- `POST /posts` - Create post
- `GET /posts/{id}` - Get specific post
- `PUT /posts/{id}` - Update post
- `DELETE /posts/{id}` - Delete post

### Media
- `POST /media/upload` - Upload media file
- `GET /media/{id}` - Get media metadata
- `DELETE /media/{id}` - Delete media file

### Channels
- `GET /channels` - List channels
- `POST /channels` - Create channel
- `GET /channels/{id}` - Get channel details
- `POST /channels/{id}/join` - Join channel
- `DELETE /channels/{id}/leave` - Leave channel

## Development Notes

⚠️ **This setup is for development only!**
- Uses LocalStack for AWS service emulation
- Default credentials are insecure
- All services are accessible without proper authentication
- Do not use in production

## Troubleshooting

### Common Issues
1. **LocalStack not starting**: Check Docker memory allocation (6GB+ recommended)
2. **Port conflicts**: Ensure ports 4566, 4510-4559 are available
3. **Lambda deployment fails**: Check function package size and dependencies
4. **Database connection issues**: Verify Aurora cluster is running in LocalStack

### Useful Commands
```bash
# Check LocalStack health
curl http://localhost:4566/_localstack/health

# List S3 buckets
aws --endpoint-url=http://localhost:4566 s3 ls

# List Lambda functions
aws --endpoint-url=http://localhost:4566 lambda list-functions

# Check Cognito User Pools
aws --endpoint-url=http://localhost:4566 cognito-idp list-user-pools --max-results 10
```

## Support

For issues with this AWS backend setup:
1. Check LocalStack logs: `docker-compose logs localstack`
2. Verify services are running: `docker-compose ps`
3. Check the LocalStack documentation: https://docs.localstack.cloud/
