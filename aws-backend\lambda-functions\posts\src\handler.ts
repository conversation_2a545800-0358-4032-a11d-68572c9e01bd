/**
 * GameFlex Posts Lambda Functions
 * Handles post creation, retrieval, and management
 */

import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { CognitoIdentityProviderClient, GetUserCommand } from '@aws-sdk/client-cognito-identity-provider';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, PutCommand, QueryCommand, UpdateCommand, ScanCommand } from '@aws-sdk/lib-dynamodb';
import { v4 as uuidv4 } from 'uuid';

// AWS clients
const cognitoClient = new CognitoIdentityProviderClient({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:45660',
    region: process.env.AWS_DEFAULT_REGION || 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    }
});

// DynamoDB configuration
const dynamoClient = new DynamoDBClient({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:45660',
    region: process.env.AWS_DEFAULT_REGION || 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    }
});

const docClient = DynamoDBDocumentClient.from(dynamoClient);

// S3 configuration
const MEDIA_BUCKET = process.env.S3_BUCKET_MEDIA || 'gameflex-media-development';

interface User {
    id: string;
    cognito_user_id: string;
    email: string;
    username: string;
    display_name?: string;
    avatar_url?: string;
}

interface Post {
    id: string;
    content: string;
    like_count: number;
    comment_count: number;
    view_count: number;
    created_at: Date;
    updated_at: Date;
    author_id: string;
    username: string;
    display_name?: string;
    avatar_url?: string;
    channel_id?: string;
    channel_name?: string;
    media_id?: string;
    filename?: string;
    extension?: string;
    media_type?: string;
    width?: number;
    height?: number;
    s3_bucket?: string;
    s3_key?: string;
}

function createCorsResponse(statusCode: number, body: any): APIGatewayProxyResult {
    return {
        statusCode,
        headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Amz-Date, X-Api-Key, X-Amz-Security-Token'
        },
        body: JSON.stringify(body)
    };
}

async function getUserFromToken(accessToken: string): Promise<User | null> {
    try {
        // Get user info from Cognito
        const getUserCommand = new GetUserCommand({
            AccessToken: accessToken
        });

        const response = await cognitoClient.send(getUserCommand);
        const cognitoUserId = response.Username!;

        // Get user from database
        const client = await dbPool.connect();
        try {
            const result = await client.query(`
                SELECT id, cognito_user_id, email, username, display_name, avatar_url
                FROM users 
                WHERE cognito_user_id = $1 AND is_active = true
            `, [cognitoUserId]);

            return result.rows[0] || null;
        } finally {
            client.release();
        }
    } catch (error) {
        console.error('Failed to get user from token:', error);
        return null;
    }
}

async function getPostsHandler(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    try {
        const queryParams = event.queryStringParameters || {};
        const limit = Math.min(parseInt(queryParams.limit || '20'), 100);
        const offset = parseInt(queryParams.offset || '0');
        const channelId = queryParams.channel_id;
        const authorId = queryParams.author_id;

        // Build query
        const whereConditions = ["p.visibility = 'public'"];
        const params: any[] = [];
        let paramIndex = 1;

        if (channelId) {
            whereConditions.push(`p.channel_id = $${paramIndex++}`);
            params.push(channelId);
        }

        if (authorId) {
            whereConditions.push(`p.author_id = $${paramIndex++}`);
            params.push(authorId);
        }

        const whereClause = whereConditions.join(' AND ');

        // Get posts
        const client = await dbPool.connect();
        try {
            const query = `
                SELECT 
                    p.id, p.content, p.like_count, p.comment_count, p.view_count,
                    p.created_at, p.updated_at,
                    u.id as author_id, u.username, u.display_name, u.avatar_url,
                    c.id as channel_id, c.name as channel_name,
                    m.id as media_id, m.filename, m.extension, m.type as media_type,
                    m.width, m.height, m.s3_bucket, m.s3_key
                FROM posts p
                JOIN users u ON p.author_id = u.id
                LEFT JOIN channels c ON p.channel_id = c.id
                LEFT JOIN media m ON p.media_id = m.id
                WHERE ${whereClause}
                ORDER BY p.created_at DESC
                LIMIT $${paramIndex++} OFFSET $${paramIndex++}
            `;

            params.push(limit, offset);
            const result = await client.query(query, params);

            // Get total count
            const countQuery = `
                SELECT COUNT(*) 
                FROM posts p 
                WHERE ${whereClause}
            `;
            const countResult = await client.query(countQuery, params.slice(0, -2));
            const totalCount = parseInt(countResult.rows[0].count);

            // Format posts
            const formattedPosts = result.rows.map((post: Post) => ({
                id: post.id,
                content: post.content,
                like_count: post.like_count,
                comment_count: post.comment_count,
                view_count: post.view_count,
                created_at: post.created_at.toISOString(),
                updated_at: post.updated_at.toISOString(),
                author: {
                    id: post.author_id,
                    username: post.username,
                    display_name: post.display_name,
                    avatar_url: post.avatar_url
                },
                channel: post.channel_id ? {
                    id: post.channel_id,
                    name: post.channel_name
                } : null,
                media: post.media_id ? {
                    id: post.media_id,
                    filename: post.filename,
                    extension: post.extension,
                    type: post.media_type,
                    width: post.width,
                    height: post.height,
                    url: post.s3_key ? `http://localhost:45660/${post.s3_bucket}/${post.s3_key}` : null
                } : null
            }));

            return createCorsResponse(200, {
                posts: formattedPosts,
                pagination: {
                    limit,
                    offset,
                    total: totalCount,
                    has_more: offset + limit < totalCount
                }
            });
        } finally {
            client.release();
        }

    } catch (error) {
        console.error('Get posts handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}

async function createPostHandler(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    try {
        const authHeader = event.headers.Authorization || event.headers.authorization || '';

        if (!authHeader.startsWith('Bearer ')) {
            return createCorsResponse(401, {
                error: 'Authorization header required'
            });
        }

        const accessToken = authHeader.substring(7);

        // Get user from token
        const user = await getUserFromToken(accessToken);
        if (!user) {
            return createCorsResponse(401, {
                error: 'Invalid or expired token'
            });
        }

        const body = JSON.parse(event.body || '{}');
        const content = body.content?.trim();
        const channelId = body.channel_id;
        const mediaId = body.media_id;

        if (!content) {
            return createCorsResponse(400, {
                error: 'Content is required'
            });
        }

        // Validate channel membership if channel_id provided
        if (channelId) {
            const client = await dbPool.connect();
            try {
                const result = await client.query(`
                    SELECT 1 FROM channel_members 
                    WHERE channel_id = $1 AND user_id = $2
                `, [channelId, user.id]);

                if (result.rows.length === 0) {
                    return createCorsResponse(403, {
                        error: 'You are not a member of this channel'
                    });
                }
            } finally {
                client.release();
            }
        }

        // Create post
        const client = await dbPool.connect();
        try {
            const postId = uuidv4();
            const now = new Date();

            const result = await client.query(`
                INSERT INTO posts (id, content, media_id, author_id, channel_id, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                RETURNING id, content, like_count, comment_count, view_count, created_at, updated_at
            `, [postId, content, mediaId, user.id, channelId, now, now]);

            const post = result.rows[0];

            // Update channel post count if applicable
            if (channelId) {
                await client.query(`
                    UPDATE channels 
                    SET post_count = post_count + 1, updated_at = $1
                    WHERE id = $2
                `, [now, channelId]);
            }

            return createCorsResponse(201, {
                message: 'Post created successfully',
                post: {
                    id: post.id,
                    content: post.content,
                    like_count: post.like_count,
                    comment_count: post.comment_count,
                    view_count: post.view_count,
                    created_at: post.created_at.toISOString(),
                    updated_at: post.updated_at.toISOString(),
                    author: {
                        id: user.id,
                        username: user.username,
                        display_name: user.display_name,
                        avatar_url: user.avatar_url
                    }
                }
            });
        } finally {
            client.release();
        }

    } catch (error) {
        console.error('Create post handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}

async function getPostHandler(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    try {
        const postId = event.pathParameters?.id;

        if (!postId) {
            return createCorsResponse(400, {
                error: 'Post ID is required'
            });
        }

        const client = await dbPool.connect();
        try {
            const result = await client.query(`
                SELECT 
                    p.id, p.content, p.like_count, p.comment_count, p.view_count,
                    p.created_at, p.updated_at,
                    u.id as author_id, u.username, u.display_name, u.avatar_url,
                    c.id as channel_id, c.name as channel_name,
                    m.id as media_id, m.filename, m.extension, m.type as media_type,
                    m.width, m.height, m.s3_bucket, m.s3_key
                FROM posts p
                JOIN users u ON p.author_id = u.id
                LEFT JOIN channels c ON p.channel_id = c.id
                LEFT JOIN media m ON p.media_id = m.id
                WHERE p.id = $1 AND p.visibility = 'public'
            `, [postId]);

            if (result.rows.length === 0) {
                return createCorsResponse(404, {
                    error: 'Post not found'
                });
            }

            const post = result.rows[0];

            // Increment view count
            await client.query(`
                UPDATE posts 
                SET view_count = view_count + 1 
                WHERE id = $1
            `, [postId]);

            // Format post
            const postData = {
                id: post.id,
                content: post.content,
                like_count: post.like_count,
                comment_count: post.comment_count,
                view_count: post.view_count + 1,
                created_at: post.created_at.toISOString(),
                updated_at: post.updated_at.toISOString(),
                author: {
                    id: post.author_id,
                    username: post.username,
                    display_name: post.display_name,
                    avatar_url: post.avatar_url
                },
                channel: post.channel_id ? {
                    id: post.channel_id,
                    name: post.channel_name
                } : null,
                media: post.media_id ? {
                    id: post.media_id,
                    filename: post.filename,
                    extension: post.extension,
                    type: post.media_type,
                    width: post.width,
                    height: post.height,
                    url: post.s3_key ? `http://localhost:45660/${post.s3_bucket}/${post.s3_key}` : null
                } : null
            };

            return createCorsResponse(200, {
                post: postData
            });
        } finally {
            client.release();
        }

    } catch (error) {
        console.error('Get post handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}

export const handler = async (event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> => {
    // Handle CORS preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return createCorsResponse(200, {});
    }

    const path = event.path;
    const method = event.httpMethod;

    try {
        if (path === '/posts' && method === 'GET') {
            return await getPostsHandler(event);
        } else if (path === '/posts' && method === 'POST') {
            return await createPostHandler(event);
        } else if (path.startsWith('/posts/') && method === 'GET') {
            return await getPostHandler(event);
        } else {
            return createCorsResponse(404, {
                error: 'Endpoint not found'
            });
        }
    } catch (error) {
        console.error('Lambda handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
};
